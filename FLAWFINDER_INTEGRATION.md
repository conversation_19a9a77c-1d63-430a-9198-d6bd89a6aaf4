# Flawfinder Integration in RepairAgent

This document describes the integration of flawfinder as a security analysis tool in the RepairAgent system.

## Overview

Flawfinder is a static analysis tool that examines C/C++ source code for potential security vulnerabilities. It has been integrated into the RepairAgent to help identify and validate fixes for security issues in C/C++ code.

## Changes Made

### 1. FlawfinderTool Class (lines 72-77)

Added a new `FlawfinderTool` class that extends the base `Tool` class:

```python
class FlawfinderTool(Tool):
    name = 'flawfinder'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['flawfinder'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr
```

### 2. Updated Prompt Template (lines 79-105)

Modified the `PROMPT_TEMPLATE` to include flawfinder in the available tools:

- Added flawfinder to the list of available tools
- Updated instructions to use flawfinder for scanning C/C++ code
- Added guidance to use flawfinder for validating fixes

Key additions:
- `"  - flawfinder (run flawfinder static analysis security scanner on C/C++ code)\n"`
- `"- Use flawfinder to scan for security vulnerabilities in C/C++ code and validate fixes\n"`
- `"Keep changes minimal. After edits, use flawfinder to validate that the vulnerability has been fixed.\n"`

### 3. Tool Instance Creation (lines 151-164)

Added flawfinder to the tool instances in the RepairAgent constructor:

```python
# Add flawfinder tool for C/C++ security analysis
self.tool_instances['flawfinder'] = FlawfinderTool()
```

### 4. Tools Schema (lines 263-268)

Added flawfinder to the function-calling tools schema:

```python
{
    "type": "function",
    "function": {
        "name": "flawfinder",
        "description": "Run flawfinder static analysis security scanner on C/C++ code. Example: args=['--html', '/workspace/vuln/file.c'] or args=['/workspace/vuln/']",
        "parameters": common_args_schema
    }
},
```

### 5. Environment Setup (lines 386-395)

Updated the `setup_environment` method to install flawfinder in the Docker container:

```python
cmds=[
    'apt-get update && apt-get install -y git flawfinder'
]
```

### 6. Tool Call Handling (lines 673-675)

Added flawfinder handling in the `run_react_loop` method:

```python
# Security analysis tools
elif name == "flawfinder":
    result = self._handle_run_tool({"tool": name, "args": args.get("args", [])})
```

## Usage

The RepairAgent can now use flawfinder through function calls. Example usage patterns:

1. **Scan entire vulnerable directory:**
   ```json
   {
     "name": "flawfinder",
     "args": ["/workspace/vuln/"]
   }
   ```

2. **Scan specific file:**
   ```json
   {
     "name": "flawfinder", 
     "args": ["/workspace/vuln/vulnerable_file.c"]
   }
   ```

3. **Generate HTML report:**
   ```json
   {
     "name": "flawfinder",
     "args": ["--html", "/workspace/vuln/vulnerable_file.c"]
   }
   ```

4. **Quiet mode with specific risk level:**
   ```json
   {
     "name": "flawfinder",
     "args": ["--quiet", "--minlevel=2", "/workspace/vuln/"]
   }
   ```

## Benefits

1. **Vulnerability Detection**: Automatically identifies common C/C++ security issues like buffer overflows, format string vulnerabilities, etc.

2. **Fix Validation**: Can be used before and after applying patches to verify that vulnerabilities have been addressed.

3. **Comprehensive Analysis**: Provides detailed reports with line numbers and risk levels for identified issues.

4. **Integration with Workflow**: Seamlessly integrated into the RepairAgent's function-calling interface.

## Common Flawfinder Options

- `--html`: Generate HTML output
- `--quiet`: Suppress normal output
- `--minlevel=N`: Only report vulnerabilities at or above level N (0-5)
- `--context`: Show context around vulnerable lines
- `--dataonly`: Don't display headers and footers
- `--singleline`: Single line output format

## Testing

A test script `test_flawfinder_integration.py` has been created to verify the integration. The test checks:

1. FlawfinderTool class functionality
2. Proper inclusion in RepairAgent tools schema
3. Presence in prompt template
4. Tool instance creation

## Notes

- Flawfinder is particularly effective for C/C++ code analysis
- The tool is installed via apt-get in the Docker container during environment setup
- All flawfinder output (stdout and stderr) is captured and returned to the agent
- The tool integrates with the existing function-calling framework without requiring changes to the core agent logic
