Create a new class representing a ReAct repair agent where you use the AssistantAgent from autogen. The class should receive the following init inputs:
def __init__(
        self,
        vuln_path: str,
        host_repo_dir: str,
        tools_allowed: List[str],
        model_choice: str = 'openai',
        client_api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: Optional[str] = None,
        container_root: str = '/workspace/vuln'
    )

It 