#!/usr/bin/env python3
"""
Example showing how RepairAgent would use flawfinder to analyze and fix vulnerabilities.

This is a conceptual example showing the workflow - it doesn't actually run the agent
but demonstrates the expected function calls and responses.
"""

def example_repair_workflow():
    """
    Example workflow showing how RepairAgent would use flawfinder
    to identify and fix vulnerabilities in C/C++ code.
    """
    
    print("=== RepairAgent Flawfinder Integration Example ===\n")
    
    # Example vulnerable C code that would be analyzed
    vulnerable_code = '''
#include <stdio.h>
#include <string.h>

void vulnerable_function(char *input) {
    char buffer[100];
    strcpy(buffer, input);  // Potential buffer overflow
    printf("Data: %s\\n", buffer);
}

int main() {
    char user_input[1000];
    printf("Enter data: ");
    gets(user_input);  // Dangerous function - buffer overflow
    vulnerable_function(user_input);
    return 0;
}
'''
    
    print("1. Initial vulnerable code:")
    print(vulnerable_code)
    print("\n" + "="*50 + "\n")
    
    # Step 1: Agent would call flawfinder to scan for vulnerabilities
    print("2. Agent calls flawfinder to scan for vulnerabilities:")
    print("Function call: flawfinder(['/workspace/vuln/vulnerable.c'])")
    
    # Expected flawfinder output (simulated)
    flawfinder_output = '''
Flawfinder version 2.0.19, (C) 2001-2019 David A. Wheeler.
Number of rules (primarily dangerous function names) in C/C++ ruleset: 223
Examining /workspace/vuln/vulnerable.c

/workspace/vuln/vulnerable.c:6:  [4] (buffer) strcpy:
  Does not check for buffer overflows when copying to destination [MS-banned]
  (CWE-120). Consider using snprintf, strcpy_s, or strlcpy (warning: strncpy
  easily misused).
/workspace/vuln/vulnerable.c:12:  [4] (buffer) gets:
  Does not check for buffer overflows (CWE-120, CWE-20). Use fgets() instead.

ANALYSIS SUMMARY:
Hits = 2
Lines analyzed = 15
Physical Source Lines of Code (SLOC) = 11
Hits@level = [0]   0 [1]   0 [2]   0 [3]   0 [4]   2 [5]   0
Hits@level+ = [0+]   2 [1+]   2 [2+]   2 [3+]   2 [4+]   2 [5+]   0
Hits/KSLOC@level+ = [0+] 181.818 [1+] 181.818 [2+] 181.818 [3+] 181.818 [4+] 181.818 [5+]   0
'''
    
    print("Flawfinder output:")
    print(flawfinder_output)
    print("\n" + "="*50 + "\n")
    
    # Step 2: Agent analyzes the output and identifies functions to fix
    print("3. Agent identifies vulnerabilities and gets function definitions:")
    print("Function call: get_function_names()")
    print("Response: Functions found: vulnerable_function, main")
    print()
    print("Function call: get_function_definition('vulnerable_function')")
    print("Function call: get_function_definition('main')")
    print("\n" + "="*50 + "\n")
    
    # Step 3: Agent applies fixes
    print("4. Agent applies security fixes:")
    
    # Fix 1: Replace strcpy with strncpy
    print("Fix 1 - Replace strcpy with strncpy:")
    print("Function call: replace_in_function('vulnerable_function', [")
    print("  {'old': 'strcpy(buffer, input);', 'new': 'strncpy(buffer, input, sizeof(buffer) - 1);\\n    buffer[sizeof(buffer) - 1] = \'\\\\0\';'}")
    print("])")
    print()
    
    # Fix 2: Replace gets with fgets
    print("Fix 2 - Replace gets with fgets:")
    print("Function call: replace_in_function('main', [")
    print("  {'old': 'gets(user_input);', 'new': 'fgets(user_input, sizeof(user_input), stdin);'}")
    print("])")
    print("\n" + "="*50 + "\n")
    
    # Step 4: Agent validates the fix
    print("5. Agent validates the fix with flawfinder:")
    print("Function call: flawfinder(['/workspace/vuln/vulnerable.c'])")
    
    # Expected output after fixes (simulated)
    fixed_output = '''
Flawfinder version 2.0.19, (C) 2001-2019 David A. Wheeler.
Number of rules (primarily dangerous function names) in C/C++ ruleset: 223
Examining /workspace/vuln/vulnerable.c

/workspace/vuln/vulnerable.c:6:  [1] (buffer) strncpy:
  Easily used incorrectly; doesn't always \\0-terminate or check for invalid
  pointers [MS-banned] (CWE-120).

ANALYSIS SUMMARY:
Hits = 1
Lines analyzed = 16
Physical Source Lines of Code (SLOC) = 12
Hits@level = [0]   0 [1]   1 [2]   0 [3]   0 [4]   0 [5]   0
Hits@level+ = [0+]   1 [1+]   1 [2+]   0 [3+]   0 [4+]   0 [5+]   0
Hits/KSLOC@level+ = [0+] 83.333 [1+] 83.333 [2+]   0 [3+]   0 [4+]   0 [5+]   0
'''
    
    print("Flawfinder output after fixes:")
    print(fixed_output)
    print("\n" + "="*50 + "\n")
    
    # Step 5: Agent finalizes the repair
    print("6. Agent analysis:")
    print("- Reduced high-risk vulnerabilities from 2 to 0")
    print("- Only 1 low-risk warning remains (strncpy usage note)")
    print("- Critical buffer overflow vulnerabilities have been fixed")
    print()
    print("Function call: finalize_repair(is_vulnerability_fixed=True)")
    print("\n" + "="*50 + "\n")
    
    # Show the final fixed code
    fixed_code = '''
#include <stdio.h>
#include <string.h>

void vulnerable_function(char *input) {
    char buffer[100];
    strncpy(buffer, input, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\\0';
    printf("Data: %s\\n", buffer);
}

int main() {
    char user_input[1000];
    printf("Enter data: ");
    fgets(user_input, sizeof(user_input), stdin);
    vulnerable_function(user_input);
    return 0;
}
'''
    
    print("7. Final fixed code:")
    print(fixed_code)
    
    print("=== Repair Complete ===")
    print("Summary:")
    print("- Replaced gets() with fgets() to prevent buffer overflow")
    print("- Replaced strcpy() with strncpy() and added null termination")
    print("- Validated fix with flawfinder - no high-risk vulnerabilities remain")

if __name__ == "__main__":
    example_repair_workflow()
