#!/usr/bin/env python3
"""
Test script to verify flawfinder integration in RepairAgent.
"""

import os
import tempfile
import subprocess
from utils.repair_agent import RepairAgent, FlawfinderTool

def test_flawfinder_tool():
    """Test the FlawfinderTool class directly."""
    print("Testing FlawfinderTool class...")
    
    # Create a simple vulnerable C code sample
    vulnerable_c_code = """
#include <stdio.h>
#include <string.h>

int main() {
    char buffer[10];
    char input[100];
    
    printf("Enter input: ");
    gets(input);  // Vulnerable function - buffer overflow
    strcpy(buffer, input);  // Another potential vulnerability
    
    printf("You entered: %s\\n", buffer);
    return 0;
}
"""
    
    # Create a temporary file with the vulnerable code
    with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
        f.write(vulnerable_c_code)
        temp_file = f.name
    
    try:
        # Test flawfinder directly on the system (if available)
        try:
            result = subprocess.run(['flawfinder', temp_file], 
                                  capture_output=True, text=True, timeout=10)
            print(f"Direct flawfinder test result:")
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            if result.stderr:
                print(f"Errors: {result.stderr}")
        except FileNotFoundError:
            print("Flawfinder not found on system - this is expected if not installed")
        except subprocess.TimeoutExpired:
            print("Flawfinder timed out")
        
    finally:
        # Clean up
        os.unlink(temp_file)

def test_repair_agent_schema():
    """Test that flawfinder is properly included in the RepairAgent tools schema."""
    print("\nTesting RepairAgent tools schema...")
    
    # Create a minimal RepairAgent instance (without actually setting up Docker)
    try:
        agent = RepairAgent(
            vuln_path="test.c",
            host_repo_dir="/tmp",
            cve_description="Test CVE",
            model_choice='openai',
            client_api_key='test',
            base_url='http://test',
            model_name='test'
        )
        
        # Get the tools schema
        tools_schema = agent._tools_schema()
        
        # Check if flawfinder is in the schema
        flawfinder_found = False
        for tool in tools_schema:
            if tool.get('function', {}).get('name') == 'flawfinder':
                flawfinder_found = True
                print("✓ Flawfinder found in tools schema")
                print(f"  Description: {tool['function']['description']}")
                break
        
        if not flawfinder_found:
            print("✗ Flawfinder NOT found in tools schema")
        
        # Check if flawfinder tool instance exists
        if 'flawfinder' in agent.tool_instances:
            print("✓ Flawfinder tool instance created")
            print(f"  Tool type: {type(agent.tool_instances['flawfinder'])}")
        else:
            print("✗ Flawfinder tool instance NOT created")
            
    except Exception as e:
        print(f"Error creating RepairAgent: {e}")

def test_prompt_template():
    """Test that flawfinder is mentioned in the prompt template."""
    print("\nTesting prompt template...")
    
    from utils.repair_agent import PROMPT_TEMPLATE
    
    if 'flawfinder' in PROMPT_TEMPLATE:
        print("✓ Flawfinder mentioned in prompt template")
        # Find the line mentioning flawfinder
        lines = PROMPT_TEMPLATE.split('\n')
        for i, line in enumerate(lines):
            if 'flawfinder' in line.lower():
                print(f"  Line {i+1}: {line.strip()}")
    else:
        print("✗ Flawfinder NOT mentioned in prompt template")

if __name__ == "__main__":
    print("Testing flawfinder integration in RepairAgent...")
    print("=" * 50)
    
    test_flawfinder_tool()
    test_repair_agent_schema()
    test_prompt_template()
    
    print("\n" + "=" * 50)
    print("Test completed!")
