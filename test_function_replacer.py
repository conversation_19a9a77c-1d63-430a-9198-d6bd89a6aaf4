from utils.function_replacer import FunctionReplacer

replacer = FunctionReplacer()

# Sample C code with a function
code = """
int add(int a, int b) {
int result = a + b;
printf("Adding numbers");
return result;
}
"""

# Test replacements
replacements = [
    {"old": "printf(\"Adding numbers\")", "new": "printf(\"Sum calculated\")"},
    {"old": "result", "new": "sum"}
]

updated_code = replacer.replace_in_function(code, "add", replacements)
print("Original code:\n", code)
print("Updated code:\n", updated_code)

# Verify replacements were made
assert "printf(\"Sum calculated\")" in updated_code
assert "int sum = a + b;" in updated_code
assert "return sum;" in updated_code
assert "printf(\"Adding numbers\")" not in updated_code

replacer = FunctionReplacer()
code = "int main() { return 0; }"


replacer.replace_in_function(code, "nonexistent", [{"old": "x", "new": "y"}])