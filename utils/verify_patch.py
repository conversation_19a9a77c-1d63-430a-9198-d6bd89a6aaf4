import os
import re
import json
import shlex
import tempfile
import subprocess
from dataclasses import dataclass
from typing import Dict, Any, Optional


def _run(cmd, *, cwd=None, timeout=None, check=False, capture=True):
    """
    Thin wrapper around subprocess.run with sane defaults and printable errors.
    Accepts str (shell=True) or list (shell=False).
    """
    shell = isinstance(cmd, str)
    result = subprocess.run(
        cmd,
        shell=shell,
        cwd=cwd,
        timeout=timeout,
        text=True,
        capture_output=capture,
        check=False,
    )
    if check and result.returncode != 0:
        msg = f"Command failed ({result.returncode}): {cmd}\nSTDOUT:\n{result.stdout}\nSTDERR:\n{result.stderr}"
        raise RuntimeError(msg)
    return result

DOCKERFILE = """\
FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Base toolchain: Java/Maven, Python, PHP, patch, etc.
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates curl git build-essential gnupg \
    openjdk-17-jdk maven \
    python3 python3-pip \
    php-cli php-xml php-mbstring php-zip unzip \
    patch \
  && rm -rf /var/lib/apt/lists/*

# Install a current Node.js (Ubuntu's nodejs is too old for Jest)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
  && apt-get update && apt-get install -y nodejs \
  && node -v && npm -v

# Python test runner
RUN pip3 install --no-cache-dir pytest

# JS test runner (pin to a major compatible with Node 20)
RUN npm install -g jest@^29.7.0

# Composer + PHPUnit
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
  && COMPOSER_ALLOW_SUPERUSER=1 composer global require phpunit/phpunit:^9 --no-interaction || true
ENV PATH="/root/.composer/vendor/bin:${PATH}"

WORKDIR /workspace
"""


@dataclass
class VerifyResult:
    image_tag: str
    container_name: str
    project_root: str
    apply_patch_ok: bool
    test_write_path: str
    test_exit_code: int
    test_stdout: str
    test_stderr: str


def _slug(value: str, default: str = "proj") -> str:
    value = (value or "").strip()
    if not value:
        return default
    # allow letters, numbers, dot, underscore, hyphen
    s = re.sub(r"[^a-zA-Z0-9._-]+", "_", value)
    return s[:80] or default

# def _run_stream(cmd, *, cwd=None, env=None, check=False):
#     """
#     Run a command and stream its stdout/stderr to the console in real time.
#     Returns an object with .returncode and .output (combined text).
#     """
#     from subprocess import Popen, PIPE, STDOUT
#     from types import SimpleNamespace

#     shell = isinstance(cmd, str)
#     proc = Popen(
#         cmd,
#         shell=shell,
#         cwd=cwd,
#         env=env,
#         stdout=PIPE,
#         stderr=STDOUT,
#         text=True,
#         bufsize=1,
#     )
#     output = []
#     try:
#         for line in proc.stdout:
#             print(line, end="")          # live progress
#             output.append(line)
#     finally:
#         retcode = proc.wait()
#     if check and retcode != 0:
#         tail = "".join(output[-200:])    # last lines for context
#         raise RuntimeError(f"Command failed ({retcode}): {cmd}\n{tail}")
#     return SimpleNamespace(returncode=retcode, output="".join(output))

def _run_stream(cmd, *, cwd=None, env=None, timeout=None, check=False):
    """
    Run a command and stream its stdout/stderr in real time.
    Supports optional timeout. Raises with the last ~200 lines on error/timeout.
    """
    from subprocess import Popen, PIPE, STDOUT, TimeoutExpired
    from types import SimpleNamespace

    shell = isinstance(cmd, str)
    proc = Popen(
        cmd,
        shell=shell,
        cwd=cwd,
        env=env,
        stdout=PIPE,
        stderr=STDOUT,
        text=True,
        bufsize=1,
    )
    output = []
    try:
        for line in iter(proc.stdout.readline, ''):
            if not line:
                break
            print(line, end='', flush=True)
            output.append(line)
        retcode = proc.wait(timeout=timeout)
    except TimeoutExpired:
        proc.kill()
        try:
            proc.wait(5)
        except Exception:
            pass
        tail = "".join(output[-200:])
        raise RuntimeError(f"Timed out running: {cmd}\n{tail}")
    finally:
        try:
            proc.stdout.close()
        except Exception:
            pass

    if check and retcode != 0:
        tail = "".join(output[-200:])
        raise RuntimeError(f"Command failed ({retcode}): {cmd}\n{tail}")
    return SimpleNamespace(returncode=retcode, output="".join(output))


class PatchVerifier:
    """
    Workflow:
      1) initialize(): build image once (if missing) and start container (if not running).
      2) verify(...): for each project/patch, create /workspace/projects/<project_id>, copy repo+patch,
         apply patch, write test, run script with HOME scoped to that project, then remove that project dir.
      3) shutdown(): stop/remove the container (optional, when all done).
    """

    def __init__(
        self,
        image_tag: str = "verify_image_multi",
        container_name: str = "verify_env",
        build_timeout: int = 1800,
        test_timeout: int = 1800,
        *,
        force_rebuild: bool = False,
    ):
        self.image_tag = image_tag
        self.container_name = container_name
        self.build_timeout = build_timeout
        self.test_timeout = test_timeout
        self.force_rebuild = force_rebuild

    # ---------- One-time lifecycle ----------

    def initialize(self):
        """Build image (if needed) and start container (if needed)."""
        self._ensure_image()
        self._ensure_container()
        # Ensure projects root exists
        self._exec("mkdir -p /workspace/projects")

    def shutdown(self):
        """Stop and remove the persistent container."""
        self._cleanup_container(keep=False)

    # ---------- Main API per project ----------
    def verify(
        self,
        patch_path: str,
        repo_path: str,
        unit: Dict[str, Any],
        *,
        project_id: Optional[str] = None,
        cleanup: bool = True,
    ) -> VerifyResult:
        """
        Args:
        patch_path: path to a local .patch file (git-style diffs supported).
        repo_path: path to a local repository directory (will be copied into container).
        unit: dict with keys:
                - "unit_test_path": str (file path where the test should be written, relative to the target dir)
                - "unit_test": str   (the test source code)
                - "shell_script": str (commands to install/build; may include 'cd ...')
        project_id: label for per-run isolation; affects /workspace/projects/<project_id>
        cleanup: remove the project directory at the end of verification (default True)
        """
        assert os.path.isfile(patch_path), f"patch_path not found: {patch_path}"
        assert os.path.isdir(repo_path), f"repo_path not found: {repo_path}"
        required = {"unit_test_path", "unit_test", "shell_script"}
        missing = required - set(unit.keys())
        if missing:
            raise ValueError(f"unit missing keys: {missing}")

        # Ensure runtime is ready even if caller forgot initialize()
        if not self._container_running():
            self.initialize()

        # Create per-project workspace
        proj = _slug(project_id or os.path.basename(os.path.abspath(repo_path)) or "proj")
        work_root = f"/workspace/projects/{proj}"
        self._exec(f"rm -rf {shlex.quote(work_root)} && mkdir -p {shlex.quote(work_root)}")

        try:
            # --- Copy inputs ---
            repo_dir = self._copy_repo_into_project(repo_path, work_root)

            # Copy patch file
            patch_dst = f"{self.container_name}:{work_root}/patch.patch"
            self._docker_cp(patch_path, patch_dst)

            # --- Initialize repo & apply patch ---
            self._init_repo(repo_dir)
            applied = self._apply_patch(repo_dir, f"{work_root}/patch.patch")
            print("Applying Patch Result:", applied)

            # --- Determine where to write tests & write test file ---
            target_dir = self._detect_target_dir(unit["shell_script"], default=repo_dir)
            test_full_path = self._join_paths(target_dir, unit["unit_test_path"])
            self._write_test_file_in_container(test_full_path, unit["unit_test"])

            # --- Install/build (user-provided script) ---
            # print("Installing dependencies / running setup ...")
            # _ = self._exec_script(
            #     unit["shell_script"],
            #     workdir=repo_dir,
            #     timeout=self.test_timeout,
            #     home_dir=f"{work_root}/home",
            # )

            # # --- Always run the unit test after install/build ---
            # print("Running unit tests ...")
            # test_res = self._run_unit_tests(target_dir, test_full_path, unit)
            
            # --- Install/build ---
            print("Installing dependencies / running setup ...")
            home_dir = f"{work_root}/home"
            _ = self._exec_script(
                unit["shell_script"],
                workdir=repo_dir,
                timeout=self.test_timeout,
                home_dir=home_dir,
            )

            # --- Run unit tests with same HOME/settings ---
            print("Running unit tests ...")
            test_res = self._run_unit_tests(target_dir, test_full_path, unit, home_dir=home_dir)

            return VerifyResult(
                image_tag=self.image_tag,
                container_name=self.container_name,
                project_root=work_root,
                apply_patch_ok=applied,
                test_write_path=test_full_path,
                test_exit_code=test_res.returncode,
                test_stdout=test_res.stdout,
                test_stderr=test_res.stderr,
            )
            

        finally:
            if cleanup:
                self._exec(f"rm -rf {shlex.quote(work_root)}")

    def _run_unit_tests(self, target_dir: str, test_full_path: str, unit: Dict[str, Any], home_dir: Optional[str] = None):
        """
        Run tests based solely on the file extension of the test we wrote.
        Java runs with the per-run Maven settings to force HTTPS for Jenkins repo.
        """
        rel_test_path = os.path.relpath(test_full_path, start=target_dir)
        _, ext = os.path.splitext(test_full_path)
        ext = ext.lower()

        set_home = f'export HOME={shlex.quote(home_dir)}; ' if home_dir else ''
        maven_settings = '-s "$HOME/.m2/settings.xml"' if home_dir else ''

        if ext == ".java":
            class_name = os.path.splitext(os.path.basename(test_full_path))[0]
            cmd = (
                "set -e; "
                f"{set_home}"
                f"mvn -U -q {maven_settings} -DskipTests=false -Dtest={shlex.quote(class_name)} test"
            )
        elif ext in {".js", ".jsx", ".mjs", ".cjs", ".ts", ".tsx", ".mts", ".cts"}:
            cmd = (
                "set -e; "
                f"(npx jest --runInBand {shlex.quote(rel_test_path)} || "
                f" jest --runInBand {shlex.quote(rel_test_path)})"
            )
        elif ext == ".php":
            cmd = f"set -e; phpunit {shlex.quote(rel_test_path)}"
        elif ext == ".py":
            cmd = f"set -e; python3 -m pytest -q {shlex.quote(rel_test_path)}"
        else:
            cmd = (
                "set -e; "
                f"echo 'No known test runner for extension: {ext}' >&2; exit 127"
            )

        return self._exec(cmd, workdir=target_dir, timeout=self.test_timeout, check=False)


    def _exec_stream(
        self,
        cmd: str,
        *,
        timeout: Optional[int] = None,
        workdir: Optional[str] = None,
        check: bool = True,
        ):
        base = ["docker", "exec"]
        if workdir:
            base += ["-w", workdir]
        base += [self.container_name, "bash", "-lc", cmd]
        return _run_stream(base, timeout=timeout, check=check)


    # ---------- Internals: Image/Container ----------

    def _image_exists(self) -> bool:
        res = _run(["docker", "image", "inspect", self.image_tag], capture=True)
        return res.returncode == 0

    def _ensure_image(self):
        if self.force_rebuild or not self._image_exists():
            self._build_image()

    def _build_image(self):
        # Build with BuildKit and plain progress so steps stream as text
        import os
        with tempfile.TemporaryDirectory(prefix="verify_image_") as tmp:
            dockerfile_path = os.path.join(tmp, "Dockerfile")
            with open(dockerfile_path, "w", encoding="utf-8") as f:
                f.write(DOCKERFILE)

            env = os.environ.copy()
            env["DOCKER_BUILDKIT"] = "1"

            cmd = [
                "docker", "build",
                "--progress=plain",          # streamable logs: "Step X/Y"
                "-t", self.image_tag,
                tmp,
            ]
            print(f"[build] Building image '{self.image_tag}' ...")
            _run_stream(cmd, env=env, check=True)
            print(f"[build] Image '{self.image_tag}' built.")


    def _container_running(self) -> bool:
        res = _run(
            ["docker", "inspect", "-f", "{{.State.Running}}", self.container_name],
            capture=True,
        )
        return res.returncode == 0 and res.stdout.strip() == "true"

    def _ensure_container(self):
        if not self._container_running():
            # Remove any stopped container with same name
            _run(f"docker rm -f {shlex.quote(self.container_name)}", capture=True)
            # Start fresh container
            _run(
                [
                    "docker",
                    "run",
                    "-d",
                    "--name",
                    self.container_name,
                    self.image_tag,
                    "bash",
                    "-lc",
                    "tail -f /dev/null",
                ],
                check=True,
            )

    def _cleanup_container(self, keep: bool):
        if keep:
            return
        _run(f"docker rm -f {shlex.quote(self.container_name)}", capture=True)

    # ---------- Internals: Exec ----------

    def _docker_cp(self, src: str, dst: str):
        _run(["docker", "cp", src, dst], check=True)

    def _exec(
        self,
        cmd: str,
        *,
        timeout: Optional[int] = None,
        workdir: Optional[str] = None,
        check: bool = True,
    ):
        base = ["docker", "exec"]
        if workdir:
            base += ["-w", workdir]
        base += [self.container_name, "bash", "-lc", cmd]
        return _run(base, timeout=timeout, check=check)

    # def _exec_script(
    #     self,
    #     script: str,
    #     *,
    #     workdir: Optional[str],
    #     timeout: Optional[int],
    #     home_dir: str,
    # ):
    #     """
    #     Run with 'set -e' so the first failing command terminates with non-zero status.
    #     Scope caches to home_dir so they are cleaned with the project.
    #     """
    #     env_setup = (
    #         "set -e;"
    #         f"export HOME={shlex.quote(home_dir)};"
    #         "mkdir -p \"$HOME/.m2\" \"$HOME/.npm\" \"$HOME/.cache/pip\" \"$HOME/.composer\";"
    #         "export PIP_CACHE_DIR=\"$HOME/.cache/pip\";"
    #         "npm config set cache \"$HOME/.npm\" --global >/dev/null 2>&1 || true;"
    #         "export COMPOSER_HOME=\"$HOME/.composer\" COMPOSER_CACHE_DIR=\"$HOME/.composer/cache\";"
    #     )
    #     wrapped = f"{env_setup} {script}"
    #     return self._exec(wrapped, timeout=timeout, workdir=workdir, check=False)
    def _exec_script(
        self,
        script: str,
        *,
        workdir: Optional[str],
        timeout: Optional[int],
        home_dir: str,
    ):
        """
        Stream logs, fail on pipeline errors, and block until background jobs finish.
        Also writes a per-project Maven settings.xml that forces HTTPS.
        """
        # Ensure Maven settings exists for this run
        self._write_maven_settings(home_dir)

        env_setup = (
            "set -Eeuo pipefail;"
            f"export HOME={shlex.quote(home_dir)};"
            "mkdir -p \"$HOME/.m2\" \"$HOME/.npm\" \"$HOME/.cache/pip\" \"$HOME/.composer\";"
            "export PIP_CACHE_DIR=\"$HOME/.cache/pip\";"
            "npm config set cache \"$HOME/.npm\" --global >/dev/null 2>&1 || true;"
            "export COMPOSER_HOME=\"$HOME/.composer\" COMPOSER_CACHE_DIR=\"$HOME/.composer/cache\";"
        )

        # Use the per-run settings.xml by default if mvn is called in the script
        script_with_settings = (
            "if command -v mvn >/dev/null 2>&1; then export MAVEN_OPTS=\"${MAVEN_OPTS:-}\"; fi; "
            + script
        )

        wrapped = f"{env_setup} {{ {script_with_settings} ; }} ; wait"
        return self._exec_stream(wrapped, timeout=timeout, workdir=workdir, check=False)



    # ---------- Internals: Repo/Patch/Test ----------

    # def _init_repo(self, repo_dir: str):
    #     init_cmds = [
    #         f'git -C {repo_dir} init',
    #         f'git -C {repo_dir} config user.email <EMAIL>',
    #         f'git -C {repo_dir} config user.name Agent',
    #         f'git -C {repo_dir} add -A',
    #         f'git -C {repo_dir} commit -m "baseline"',
    #     ]
    #     for c in init_cmds:
    #         subprocess.run(['docker','exec', self.container_name, 'bash', '-lc', c], check=True)

    def _init_repo(self, repo_dir: str):
        """
        Initialize a Git repo exactly like the working setup:
        - run everything in a single shell with 'cd'
        - set user.* locally
        - add & commit (no-op safe)
        - mark directory safe (avoids 'dubious ownership' if it ever appears)
        """
        cmd = (
            f'cd {shlex.quote(repo_dir)} && '
            'git init && '
            'git config user.email <EMAIL> && '
            'git config user.name Agent && '
            'git config --global --add safe.directory "$(pwd)" || true && '
            'git add -A || true && '
            'git commit -m "baseline" || true'
        )
        self._exec(cmd, check=True)

    # def _apply_patch(self, repo_dir: str, patch_file: str) -> bool:
    #     """
    #     Try 'git apply' first (handles git-style binary patches), then fallback to 'patch'.
    #     Returns True if the tree changed.
    #     """
    #     # Attempt git apply
    #     self._exec(
    #         f"cd {repo_dir} && git apply --binary --reject --whitespace=fix -p1 {patch_file} || true",
    #         check=False,
    #     )
    #     if self._tree_changed(repo_dir):
    #         return True

    #     # Fallback to 'patch' utility (for plain unified diffs)
    #     self._exec(
    #         f"cd {repo_dir} && patch -p1 --reject-file=- < {patch_file} || true",
    #         check=False,
    #     )
    #     return self._tree_changed(repo_dir)
    def _apply_patch(self, repo_dir: str, patch_file: str) -> bool:
        """
        Apply a patch robustly:
        - normalize CRLF to LF in the patch (helps when patch was saved on Windows)
        - try git apply with multiple strip levels, tolerant to whitespace drift
        - fall back to 'patch' with fuzzy whitespace matching
        Returns True if the working tree changed (files modified).
        """
        pf = shlex.quote(patch_file)

        # Normalize line endings in the patch to avoid context mismatches on CRLF
        # (use a temp file to stay POSIX-safe across sed variants)
        self._exec(f"sed -e 's/\\r$//' {pf} > {pf}.lf && mv {pf}.lf {pf}", check=False)

        # 1) Try git apply with multiple strip levels and whitespace tolerance
        for p in (1, 0, 2, 3):
            # --ignore-space-change tolerates indentation/space diffs in context
            # --whitespace=nowarn avoids failing due to trailing spaces, etc.
            self._exec(
                f"cd {repo_dir} && "
                f"git apply --whitespace=nowarn --ignore-space-change -p{p} {pf} || true",
                check=False,
            )
            if self._tree_changed(repo_dir):
                return True

        # 2) Fallback: POSIX patch with lenient matching
        for p in (1, 0, 2, 3):
            # -l: ignore whitespace in context; --fuzz=3: allow small context drift
            # --reject-file=- prevents creating *.rej files that could pollute tree_changed()
            self._exec(
                f"cd {repo_dir} && "
                f"patch -p{p} -l --fuzz=3 --reject-file=- < {pf} || true",
                check=False,
            )
            if self._tree_changed(repo_dir):
                return True

        return False

    
    def _copy_repo_into_project(self, host_repo: str, project_root: str) -> str:
        """
        Create the target directory and copy the *contents* of host_repo into it,
        just like the prior working setup did.

        Using 'host_repo/.' ensures Docker copies the contents rather than nesting
        an extra directory.
        """
        repo_dir = f"{project_root}/repo"
        # Ensure destination exists first, like in the working code
        self._exec(f"mkdir -p {shlex.quote(repo_dir)}")

        host_repo_abs = os.path.abspath(host_repo)
        # Copy contents (trailing '/.' is important)
        self._docker_cp(os.path.join(host_repo_abs, "."), f"{self.container_name}:{repo_dir}")
        return repo_dir


    def _tree_changed(self, repo_dir: str) -> bool:
        status = self._exec(f"git -C {repo_dir} status --porcelain", check=False)
        return bool(status.stdout.strip())

    def _detect_target_dir(self, script: str, default: str) -> str:
        """
        Finds the last 'cd <dir>' in script and resolves it relative to `default`.
        If none, returns default.
        """
        matches = list(re.finditer(r"(^|\n)\s*cd\s+([^\n;&|]+)", script))
        if not matches:
            return default
        raw = matches[-1].group(2).strip().strip('"').strip("'")
        if raw.startswith("/"):
            return os.path.normpath(raw)
        return os.path.normpath(os.path.join(default, raw))

    def _join_paths(self, base: str, rel: str) -> str:
        # Normalize to avoid accidental '../'
        rel_norm = os.path.normpath(rel).lstrip("/")
        return os.path.normpath(os.path.join(base, rel_norm))

    def _write_test_file_in_container(self, full_path: str, code: str):
        # Create parent dir and copy via a temp file for safe encoding handling.
        parent = os.path.dirname(full_path)
        self._exec(f"mkdir -p {shlex.quote(parent)}")
        with tempfile.NamedTemporaryFile("w", delete=False, encoding="utf-8") as tf:
            tf.write(code)
            tmp_host = tf.name
        try:
            self._docker_cp(tmp_host, f"{self.container_name}:{full_path}")
        finally:
            try:
                os.unlink(tmp_host)
            except OSError:
                pass

    def _write_maven_settings(self, home_dir: str):
        """
        Write a settings.xml that forces HTTPS for Jenkins repo and enables snapshots.
        """
        settings_xml = """<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                            https://maven.apache.org/xsd/settings-1.0.0.xsd">
        <mirrors>
            <!-- Force HTTPS for the Jenkins repo id used by old POMs -->
            <mirror>
            <id>jenkins-https</id>
            <mirrorOf>repo.jenkins-ci.org</mirrorOf>
            <url>https://repo.jenkins-ci.org/public/</url>
            </mirror>
            <!-- Ensure Central is HTTPS -->
            <mirror>
            <id>central-https</id>
            <mirrorOf>central</mirrorOf>
            <url>https://repo.maven.apache.org/maven2/</url>
            </mirror>
        </mirrors>
        <profiles>
            <profile>
            <id>force-https-repos</id>
            <repositories>
                <repository>
                <id>repo.jenkins-ci.org</id>
                <url>https://repo.jenkins-ci.org/public/</url>
                <releases><enabled>true</enabled></releases>
                <snapshots><enabled>true</enabled></snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                <id>repo.jenkins-ci.org</id>
                <url>https://repo.jenkins-ci.org/public/</url>
                <releases><enabled>true</enabled></releases>
                <snapshots><enabled>true</enabled></snapshots>
                </pluginRepository>
            </pluginRepositories>
            </profile>
        </profiles>
        <activeProfiles>
            <activeProfile>force-https-repos</activeProfile>
        </activeProfiles>
        </settings>"""
        # Ensure dir exists and copy settings.xml into container-scoped HOME
        m2_dir = f"{home_dir}/.m2"
        self._exec(f"mkdir -p {shlex.quote(m2_dir)}", check=True)
        with tempfile.NamedTemporaryFile("w", delete=False, encoding="utf-8") as tf:
            tf.write(settings_xml)
            tmp_host = tf.name
        try:
            self._docker_cp(tmp_host, f"{self.container_name}:{m2_dir}/settings.xml")
        finally:
            try:
                os.unlink(tmp_host)
            except OSError:
                pass

# ----------------- Convenience API -----------------

def verify_patch(
    patch_path: str,
    repo_path: str,
    unit_test_dict: Dict[str, Any],
    *,
    project_id: Optional[str] = None,
    force_rebuild: bool = False,
) -> Dict[str, Any]:
    """
    Build/start once on demand, verify, then delete only that project's files.
    """
    runner = PatchVerifier(force_rebuild=force_rebuild)
    runner.initialize()
    res = runner.verify(patch_path, repo_path, unit_test_dict, project_id=project_id, cleanup=True)
    # keep container alive for interactive debugging; comment the next line if you want to stop it:
    # runner.shutdown()
    return {
        "image_tag": res.image_tag,
        "container_name": res.container_name,
        "project_root": res.project_root,
        "apply_patch_ok": res.apply_patch_ok,
        "test_write_path": res.test_write_path,
        "test_exit_code": res.test_exit_code,
        "test_stdout": res.test_stdout,
        "test_stderr": res.test_stderr,
    }


if __name__ == "__main__":
    import argparse

    p = argparse.ArgumentParser(description="Verify a patch (initialize once, per-project cleanup).")
    p.add_argument("--patch", required=True, help="Path to .patch file")
    p.add_argument("--repo", required=True, help="Path to local repo dir")
    p.add_argument("--unit", required=True, help="Path to JSON with unit_test_path/unit_test/shell_script")
    p.add_argument("--project-id", default=None, help="Identifier for the project workspace")
    p.add_argument("--force-rebuild", action="store_true", help="Force rebuilding the image once before running")
    args = p.parse_args()

    with open(args.unit, "r", encoding="utf-8") as f:
        unit = json.load(f)

    verifier = PatchVerifier(force_rebuild=args.force_rebuild)
    verifier.initialize()
    result = verifier.verify(args.patch, args.repo, unit, project_id=args.project_id, cleanup=True)
    # Optionally reuse verifier for more projects here...
    # verifier.shutdown()  # when completely done

    print(json.dumps({
        "image_tag": result.image_tag,
        "container_name": result.container_name,
        "project_root": result.project_root,
        "apply_patch_ok": result.apply_patch_ok,
        "test_write_path": result.test_write_path,
        "test_exit_code": result.test_exit_code,
        "test_stdout": result.test_stdout,
        "test_stderr": result.test_stderr,
    }, indent=2))
