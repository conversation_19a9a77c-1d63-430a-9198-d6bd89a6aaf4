import os
import requests
import git
from urllib.parse import urlparse


class CVERepo:
    def __init__(
        self,
        cve_id: str,
        cve_desc: str,
        repo_url: str,
        commit_hash: str,
        repo_dir: str
    ):
        self.cve_id = cve_id
        self.cve_desc = cve_desc
        self.repo_url = self._normalize_repo_url(repo_url)  # ensure full URL
        self.commit_hash = commit_hash
        self.repo_dir = repo_dir

    def _normalize_repo_url(self, url: str) -> str:
        """
        Accepts formats like:
          - 'owner/repo'
          - 'github.com/owner/repo'
          - 'https://github.com/owner/repo(.git)'
        Returns 'https://github.com/owner/repo' (no trailing .git).
        """
        u = (url or "").strip()
        if not u:
            raise ValueError("Empty repository URL")

        if u.startswith("http://") or u.startswith("https://"):
            base = u
        elif u.startswith("github.com/"):
            base = "https://" + u
        else:
            # assume 'owner/repo'
            parts = u.split("/")
            if len(parts) == 2 and all(parts):
                base = "https://github.com/" + u
            else:
                raise ValueError(f"Unsupported repo URL format: {url}")

        # strip trailing '.git' and trailing slash
        if base.endswith(".git"):
            base = base[:-4]
        return base.rstrip("/")

    # def clone_and_checkout_previous_commit(self):
    #     # Clone or open existing repo
    #     if not os.path.exists(self.repo_dir):
    #         print(f"[{self.cve_id}] Cloning repository...")
    #         repo = git.Repo.clone_from(
    #         self.repo_url, 
    #         self.repo_dir,
    #         progress=ProgressPrinter()
    #     )
    #         print(f"[{self.cve_id}] Clone completed")
    #     else:
    #         repo = git.Repo(self.repo_dir)
    #         print(f"[{self.cve_id}] Using existing repository")

    #     try:
    #         # Drop any local changes and untracked/ignored files
    #         repo.git.reset('--hard')
    #         repo.git.clean('-xfd')
    #     except Exception as e:
    #         print(f"[{self.cve_id}] Warning: Could not reset repo state: {e}")

    #     # Make sure we have the target commit locally
    #     try:
    #         repo.git.fetch("origin", self.commit_hash, depth=2)
    #     except Exception:
    #         # As a fallback, fetch everything (heavy, but robust)
    #         repo.git.fetch("--all")

    #     # Checkout the commit hash directly
    #     repo.git.checkout(self.commit_hash)
    #     print(f"Repository ready at commit: {self.commit_hash}")

    def clone_and_checkout_previous_commit(self):
        cve_id = self.cve_id

        class ProgressPrinter(git.RemoteProgress):
            def update(self, op_code, cur_count, max_count=None, message=''):
                if message:
                    print(f"[{cve_id}] {message}")
                    
        # Clone or open existing repo
        if not os.path.exists(self.repo_dir):
            print(f"[{self.cve_id}] Cloning repository...")
            repo = git.Repo.clone_from(
                self.repo_url, 
                self.repo_dir,
                progress=ProgressPrinter()
            )
            print(f"[{self.cve_id}] Clone completed")
        else:
            repo = git.Repo(self.repo_dir)
            print(f"[{self.cve_id}] Using existing repository")

        try:
            # More aggressive cleanup - stash any changes first
            try:
                repo.git.stash('push', '-u', '-m', 'auto-stash before checkout')
            except Exception:
                pass  # No changes to stash
            
            # Drop any local changes and untracked/ignored files
            repo.git.reset('--hard', 'HEAD')
            repo.git.clean('-xfd')
            
            # Clear any stashes
            try:
                repo.git.stash('clear')
            except Exception:
                pass
                
        except Exception as e:
            print(f"[{self.cve_id}] Warning: Could not reset repo state: {e}")

        # Make sure we have the target commit locally
        try:
            repo.git.fetch("origin", self.commit_hash, depth=2)
        except Exception:
            # As a fallback, fetch everything (heavy, but robust)
            repo.git.fetch("--all")

        # Force checkout the commit hash
        try:
            repo.git.checkout(self.commit_hash, force=True)
        except Exception as e:
            # Last resort: detach HEAD and force checkout
            print(f"[{self.cve_id}] Force checkout failed, trying detached HEAD...")
            repo.git.checkout('--detach', '--force', self.commit_hash)
        
        print(f"Repository ready at commit: {self.commit_hash}")