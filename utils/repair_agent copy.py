import os
import subprocess
import time
import json
from typing import List, Tuple, Optional, Dict, Any

# Abstract tool base class
class Tool:
    """
    Abstract base class for tools available within the security agent.
    """
    name: str

    def run(self, *args: str) -> str:
        """
        Execute the tool with provided arguments and return combined stdout and stderr.
        """
        raise NotImplementedError

# Concrete tool implementations
class LsTool(Tool):
    name = 'ls'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['ls'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class CatTool(Tool):
    name = 'cat'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['cat'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class GitTool(Tool):
    name = 'git'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['git'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class BanditTool(Tool):
    name = 'bandit'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['bandit'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class PylintTool(Tool):
    name = 'pylint'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['pylint'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class MypyTool(Tool):
    name = 'mypy'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['mypy'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

class ProspectorTool(Tool):
    name = 'prospector'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['prospector'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

# Main agent class
class RepairAgent:
    """
    A ReAct-based agent for automated vulnerability patching inside a Docker container.

    Attributes:
        vuln_path (str): Absolute host path to the vulnerable code directory.
        info_level (str): One of 'blackbox', 'midbox', 'whitebox'.
        info_content (str): Detailed input corresponding to info_level.
        tools_allowed (List[str]): Keys of allowed tools.
        model_choice (str): 'openai' or 'ollama'.
        client_api_key, base_url, model_name: optional config.
        container (str): Docker container name.
        tool_instances (Dict[str, Tool]): Instantiated tool objects.
    """

    PROMPT_TEMPLATE = (
        "You are a security-fix agent running within a Docker container.\n"
        "Your mission is to locate and fix a software vulnerability by iteratively reasoning and executing only the allowed tools via function calls.\n"
        "Follow the ReACT paradigm: generate your reasoning in <THOUGHT> tags. When ready, select a tool and run it using a function call.\n"
        "Strictly enforce the following format for every LLM response (without deviation):\n"
        "^<THOUGHT>[\\s\\S]*?</THOUGHT>$\n"
        "If the format is incorrect, reply: 'Output format incorrect, please follow the specified format.' and then emit only the tags.\n"
        "Available tools (called via function-calling interface):\n"
        "  - ls\n"
        "  - cat\n"
        "  - git\n"
        "  - bandit\n"
        "  - pylint\n"
        "  - mypy\n"
        "  - prospector\n"
        "  - finalize_repair (used when you believe the vulnerability has been fixed)\n"
        "<INPUT>\n"
        "vuln_path: {vuln_path}\n"
        "info_level: {info_level}\n"
        "info_content: {info_content}\n"
        "tools_allowed: {tools_allowed}\n"
        "</INPUT>\n"
        "<GOAL>\n"
        "Based on info_level and provided info_content, locate the vulnerable code segments, apply the minimal patch via git commands, and validate the fix using the available tools.\n"
        "Once you are confident the vulnerability is fixed, call the finalize_repair function to end the task.\n"
        "</GOAL>\n"
        "<FORMAT>\n"
        "Each cycle: generate one <THOUGHT> with reasoning. Do not emit tool commands in text; tools will be invoked via function calls.\n"
        "After seeing the result of a tool call, continue with the next <THOUGHT>.\n"
        "When you believe the vulnerability is fixed and validated, call finalize_repair via function calling.\n"
        "</FORMAT>"
    )


    def __init__(
        self,
        vuln_path: str,
        host_repo_dir: str,
        info_level: str,
        info_content: str,
        tools_allowed: List[str],
        model_choice: str = 'openai',
        client_api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: Optional[str] = None,
    ):
        self.vuln_path = vuln_path
        self.host_repo_dir = host_repo_dir
        self.info_level = info_level
        self.info_content = info_content
        self.tools_allowed = tools_allowed
        self.model_choice = model_choice
        self.client_api_key = client_api_key
        self.base_url = base_url
        self.model_name = model_name
        self.container = f"agent_env_{int(time.time())}"
        # instantiate allowed tools
        self.tool_instances: Dict[str, Tool] = {}
        for key in tools_allowed:
            if key == 'ls': self.tool_instances['ls'] = LsTool()
            if key == 'cat': self.tool_instances['cat'] = CatTool()
            if key == 'git': self.tool_instances['git'] = GitTool()
            if key == 'bandit': self.tool_instances['bandit'] = BanditTool()
            if key == 'pylint': self.tool_instances['pylint'] = PylintTool()
            if key == 'mypy': self.tool_instances['mypy'] = MypyTool()
            if key == 'prospector': self.tool_instances['prospector'] = ProspectorTool()
        # bind container to each tool
        for t in self.tool_instances.values(): setattr(t, 'container', self.container)
        # init model client
        # if model_choice == 'openai':
        #     import openai
        #     openai.api_key = client_api_key
        #     self.client = OpenAI(api_key=client_api_key)
        # elif model_choice == 'ollama':
        #     from ollama import Ollama

        #     if not client_api_key or not base_url or not model_name:
        #         raise ValueError("ollama requires client_api_key, base_url and model_name")
            
        #     self.client = Ollama(base_url=base_url)
        if model_choice == 'openai':
            from openai import OpenAI

            if not client_api_key or not base_url or not model_name:
                raise ValueError("gemini requires client_api_key, base_url and model_name")

            # instantiate the OpenAI-compatible Gemini client
            self.client = OpenAI(
                api_key=client_api_key,
                base_url=base_url
            )
        else:
            raise ValueError("model_choice must be 'openai'")


    # Tool declarations for OpenAI function calling
    def _tools_schema(self) -> List[Dict[str, Any]]:
        return [
            {
                "type": "function",
                "function": {
                    "name": "run_tool",
                    "description": (
                        "Run a whitelisted command (one of: ls, cat, git, bandit, pylint, mypy, prospector) "
                        "inside the container. Provide each argument as a separate array item. "
                        "Examples:\n"
                        "  - {'tool':'ls','args':['-la','/workspace/vuln']}\n"
                        "  - {'tool':'cat','args':['/workspace/vuln/src/app.py']}\n"
                        "  - {'tool':'git','args':['-C','/workspace/vuln','status']}\n"
                        "  - {'tool':'bandit','args':['-r','/workspace/vuln','-f','json','--quiet']}"
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "tool": {
                                "type": "string",
                                "enum": ["ls", "cat", "git", "bandit", "pylint", "mypy", "prospector"]
                            },
                            "args": {
                                "type": "array",
                                "items": {"type": "string"},
                                "default": []
                            }
                        },
                        "required": ["tool"]
                    }
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "finalize_repair",
                    "description": (
                        "Call this when you believe the vulnerability is fixed and validated. "
                        "This will produce a patch from the staged changes and copy it to the host."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "summary": {"type": "string", "description": "Short summary of the fix & validation steps."},
                            "patch_name": {"type": "string", "description": "Name (without extension) for the patch file."}
                        },
                        "required": ["summary", "patch_name"]
                    }
                }
            }
        ]
    def _build_user_context(self) -> str:
        return (
            "<INPUT>\n"
            f"vuln_path: {self.vuln_path}\n"
            f"info_level: {self.info_level}\n"
            f"info_content: {self.info_content}\n"
            f"tools_allowed: {json.dumps(self.tools_allowed)}\n"
            "</INPUT>\n"
            "<GOAL>\n"
            "Locate the vulnerable code, apply the minimal patch (via git), and validate the fix.\n"
            "When done, call finalize_repair with a short summary and a patch_name.\n"
            "</GOAL>"
        )

    def _build_prompt(self) -> str:
        return self.PROMPT_TEMPLATE.format(
            vuln_path=self.vuln_path,
            info_level=self.info_level,
            info_content=self.info_content,
            tools_allowed=json.dumps(self.tools_allowed)
        )

    def call_model(self, messages: List[dict]) -> dict:
        # if self.model_choice == 'openai':
        #     resp = self.client.create(model='gpt-4o-mini', messages=messages)
        #     return resp.choices[0].message
        
        # if self.model_choice == 'ollama':
        #     prompt = "".join([m['content'] for m in messages])
        #     out = self.client.generate(model=self.model_name, prompt=prompt)
        #     return {'role': 'assistant', 'content': out['choices'][0]['message']}
        
        if self.model_choice == 'openai':
            resp = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages
            )
            # return resp.choices[0].message
            return {'role': 'assistant', 'content': resp.choices[0].message.content}

        raise ValueError("model_choice not supported.")
    def parse_response(self, content: str) -> Tuple[str, str, bool]:
        thought, action, done = '', '', False
        if '<THOUGHT>' in content and '</THOUGHT>' in content:
            thought = content.split('<THOUGHT>')[1].split('</THOUGHT>')[0].strip()
        if '<ACTION>' in content and '</ACTION>' in content:
            action = content.split('<ACTION>')[1].split('</ACTION>')[0].strip()
        if '<DONE/>' in content: done = True
        return thought, action, done

    def setup_environment(self):
        subprocess.run(['docker','run','-dit','--name',self.container,'python:3.11-slim'], check=True)
        subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', '/workspace/vuln'], check=True)
        cmds=['apt-get update && apt-get install -y git']
        for key in self.tools_allowed:
            if key in ['bandit','pylint','mypy','prospector']: cmds.append(f'pip install {key}')
        for c in cmds:
            subprocess.run(['docker','exec',self.container,'bash','-lc',c], check=True)
        # subprocess.run(['docker','cp',os.path.join(self.host_repo_dir, self.vuln_path),f'{self.container}:/workspace/vuln/'], check=True)
        # Recreate the vuln_path's directory structure inside the container
        rel_path = os.path.normpath(self.vuln_path)
        parent_dir = os.path.dirname(rel_path)
        if parent_dir:
            docker_target_dir = f'/workspace/vuln/{parent_dir}'
            subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', docker_target_dir], check=True)

        # Copy the file or directory to the correct location
        host_full_path = os.path.join(self.host_repo_dir, self.vuln_path)
        container_target_path = f'{self.container}:/workspace/vuln/{self.vuln_path}'
        subprocess.run(['docker', 'cp', host_full_path, container_target_path], check=True)

        # Initialize repo
        init_cmds = [
            'git -C /workspace/vuln init',
            'git -C /workspace/vuln config user.email <EMAIL>',
            'git -C /workspace/vuln config user.name Agent',
            'git -C /workspace/vuln add -A',
            'git -C /workspace/vuln commit -m "baseline"',
        ]
        for c in init_cmds:
            subprocess.run(['docker','exec', self.container, 'bash', '-lc', c], check=True)

    def run_tool(self, action: str) -> str:
        parts=action.split(); tool_key=parts[0]; args=parts[1:]
        if tool_key not in self.tool_instances:
            return f"Error: tool '{tool_key}' not allowed."
        return self.tool_instances[tool_key].run(*args)

    def run_react_loop(self):
        messages = [
            {'role': 'system', 'content': self._build_prompt()},
            {'role': 'user', 'content': 'Begin repairing the vulnerability while following the system instructions.'}
        ]

        while True:
            resp=self.call_model(messages)
            content=resp['content']; thought,action,done=self.parse_response(content)
            messages.append({'role':'assistant','content':content})
            if not thought or not action:
                messages.append({'role':'assistant','content':'Output format incorrect, please follow the specified format.'})
                continue
            print(f'[THOUGHT] {thought}')
            print(f'[ACTION] {action}')
            obs=self.run_tool(action)
            print(f'[OBSERVATION] {obs}')
            messages.append({'role':'user','content':obs})
            if done: break

    def extract_patch(self, patch_name: str, host_dest: str):
        repo = '/workspace/vuln'
        # Stage any changes made by the agent
        self.run_tool(f'git -C {repo} add -A')

        # Ensure there are staged changes; avoid producing an empty patch
        # diff --cached --quiet exits 1 if there are differences
        check = self.run_tool(f'git -C {repo} diff --cached --quiet || echo CHANGED')
        if 'CHANGED' not in check:
            raise RuntimeError('No staged changes; patch would be empty.')

        # Produce a binary-safe patch from staged changes
        path = f'/workspace/{patch_name}.patch'
        self.run_tool(f'git -C {repo} diff --cached --binary > {path}')

        # Sanity check that the file is non-empty before copying out
        ok = subprocess.run(
            ['docker','exec',self.container,'bash','-lc', f'test -s {path} && echo OK || echo EMPTY'],
            capture_output=True, text=True, check=True
        )

        if 'EMPTY' in ok.stdout:
            raise RuntimeError('Generated patch file is empty; aborting copy.')

        os.makedirs(host_dest, exist_ok=True)
        subprocess.run(
            ['docker','cp', f'{self.container}:{path}', os.path.join(host_dest, f'{patch_name}.patch')],
            check=True
        )

    def cleanup(self):
        subprocess.run(['docker','rm','-f',self.container], check=True)