from tree_sitter import Language, Parser, Node
from tree_sitter_c import language
from tqdm import tqdm
import os
import pandas as pd
from typing import Callable, Optional, List, Dict
import re
from pathlib import Path

tqdm.pandas()

class FunctionReplacer:
    def __init__(self, parser: Parser = None):
        self.parser = parser
        
        if self.parser is None:
            self.parser, self.c_language = self.get_tree_sitter_parser()

    def get_tree_sitter_parser(self):
        """
        Get a tree-sitter parser for C language.

        Returns:
            Parser or None if failed
        """
        try:
            # Get the C language from the tree-sitter-c package
            c_language = Language(language())

            # Initialize the parser with the language
            parser = Parser(c_language)

            return parser, c_language
        except Exception as e:
            print(f"Tree-sitter initialization error: {e}")
            return None

    def get_identifiers(self, code: str):
        """
        Extract all identifiers from the given code using tree-sitter.

        Args:
            code: The source code as a string

        Returns:
            A dictionary mapping identifier names to lists of tree-sitter nodes
        """
        code_bytes = bytes(code, 'utf8')
        tree = self.parser.parse(code_bytes)
        root = tree.root_node

        identifiers = {}
        stack = [root]
        while stack:
            node = stack.pop()
            if node.type == "identifier":
                identifier_name = code_bytes[node.start_byte:node.end_byte].decode('utf8')
                
                if identifier_name in identifiers:
                    identifiers[identifier_name].append(node)
                else:
                    identifiers[identifier_name] = [node]

            stack.extend(node.children)

        return identifiers

    def get_first_function_name(self, code: str) -> str | None:
        """
        Return the name of the first function defined in *code* or None
        if no function definition is present.
        """
        code_bytes = code.encode("utf-8")
        tree       = self.parser.parse(code_bytes)
        root       = tree.root_node

        stack = [root]
        while stack:
            node = stack.pop()

            if node.type == "function_definition":
                decl = node.child_by_field_name("declarator")
                if decl:
                    # depth-first, but left-to-right
                    id_stack = [decl]
                    while id_stack:
                        n = id_stack.pop()
                        if n.type == "identifier":
                            return code_bytes[n.start_byte:n.end_byte].decode("utf-8")

                        # push children in reverse so the *first* child is popped next
                        id_stack.extend(reversed(n.children))

            stack.extend(node.children)

        return None
    
    def get_function_names(self, code: str) -> list[str]:
        """
        Return a list containing the names of *all* functions defined in *code*
        (in lexical order).  The list is empty if the translation unit contains
        no function definitions.
        """
        code_bytes = code.encode("utf-8")
        tree       = self.parser.parse(code_bytes)
        root       = tree.root_node

        names: list[str] = []
        stack = [root]

        while stack:
            node = stack.pop()

            if node.type == "function_definition":
                decl = node.child_by_field_name("declarator")
                if decl:
                    id_stack = [decl]
                    while id_stack:
                        n = id_stack.pop()
                        if n.type == "identifier":
                            names.append(code_bytes[n.start_byte:n.end_byte].decode("utf-8"))
                            break                    
                        id_stack.extend(reversed(n.children))

            stack.extend(reversed(node.children))

        return names

    def constant_transform(self, new_name: str) -> Callable[[Node, int, bytes], str]:
        """
        Factory that ignores the node/id and always returns `new_name`.
        
        Args:
            new_name: The name to return
            
        Returns:
            A function that always returns new_name
        """
        return lambda _node, _id, _bytes: new_name

    def rename_identifiers(self, code: str, transform: Callable[[Node, int, bytes], str], rename_pattern: str = r'.*(good|bad|cwe).*', cache: dict[str, str] = None):
        """
        Renames identifiers in `code` that match `rename_pattern` using `transform` function.
        
        Args:
            code: The source code or file path
            transform: Function to transform identifiers
            rename_pattern: Regex pattern to match identifiers
            cache: Optional cache of identifier mappings
            
        Returns:
            The transformed code
        """
        identifiers = self.get_identifiers(code)
        code_edits = []
        id = 0

        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code = file.read()
                code_bytes = bytearray(code, 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')

        if cache is None:
            cache = {}

        for identifier, nodes in identifiers.items():
            # Skip preprocessor directives
            if "preproc" in nodes[0].parent.type:
                continue

            # Skip identifiers that don't match the pattern
            if not re.search(rename_pattern, identifier, flags=re.IGNORECASE):
                continue

            if cache and identifier in cache:
                renamed_identifier = cache[identifier]
            else:
                renamed_identifier = transform(nodes[0], id, code_bytes)
                cache[identifier] = renamed_identifier
            
            renamed_bytes = renamed_identifier.encode('utf8')
            for node in nodes:
                code_edits.append((node.start_byte, node.end_byte, renamed_bytes))
            id += 1

        # Sort by start byte descending so earlier edits don't shift offsets
        code_edits.sort(key=lambda x: x[0], reverse=True)

        for start_byte, end_byte, renamed_bytes in code_edits:
            code_bytes[start_byte: end_byte] = renamed_bytes

        return code_bytes.decode('utf8')

    def get_function_definition(self, code: str, function_name: str) -> str:
        """
        Get the definition of a specific function from code.
        
        Args:
            code: The source code or file path
            function_name: The name of the function to retrieve
            
        Returns:
            The function definition as a string
            
        Raises:
            ValueError: If the function is not found
        """
        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code_bytes = bytearray(file.read(), 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')

        tree = self.parser.parse(code_bytes)

        # Locate definition of function_name
        function_node = None
        root = tree.root_node
        stack = [root]
        while stack:
            node = stack.pop()
            
            if node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                id_stack = [declarator]
                while id_stack:
                    child_decorator = id_stack.pop()
                    if child_decorator.type == "identifier":
                        identifier_name = code_bytes[child_decorator.start_byte:child_decorator.end_byte].decode('utf8')
                
                        if identifier_name == function_name:
                            function_node = node
                            break
                        break

                    id_stack.extend(reversed(child_decorator.children))
            stack.extend(node.children)
        
        if function_node is None:
            raise ValueError(f"No function definition for '{function_name}' found in {code}")

        # Extract and return function definition
        function_definition = code_bytes[function_node.start_byte:function_node.end_byte].decode('utf8')
        return function_definition
    
    def replace_function_definition(self, code: str, function_name: str, new_definition: str) -> str:
        """
        Replace a function definition in code with a new definition.
        
        Args:
            code: The source code or file path
            function_name: The name of the function to replace
            new_definition: The new function definition
            
        Returns:
            The updated code or file path if code was a file path
        """
        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code_bytes = bytearray(file.read(), 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')

        tree = self.parser.parse(code_bytes)

        # Locate definition of function_name
        function_node = None
        root = tree.root_node
        stack = [root]
        while stack:
            node = stack.pop()
            
            if node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                id_stack = [declarator]
                while id_stack:
                    child_decorator = id_stack.pop()
                    if child_decorator.type == "identifier":
                        identifier_name = code_bytes[child_decorator.start_byte:child_decorator.end_byte].decode('utf8')
                
                        if identifier_name == function_name:
                            print(f"Found {function_name} in {code[:min(len(code), 100)]}...")
                            function_node = node
                            break
                        break

                    id_stack.extend(reversed(child_decorator.children))
            stack.extend(node.children)
        
        if function_node is None:
            raise ValueError(f"No function definition for '{function_name}' found in {code}")

        # Replace previous definition with new definition
        code_bytes[function_node.start_byte:function_node.end_byte] = new_definition.encode("utf-8")
        updated_code = code_bytes.decode("utf-8")

        # Get the name of the new definition and update function calls to previous name
        # Note: This code is assuming the new definition has the same input and output parameters
        new_name = self.get_first_function_name(new_definition)

        if new_name != function_name:
            updated_code = self.rename_identifiers(
                updated_code,
                transform=self.constant_transform(new_name),
                rename_pattern=rf"^{re.escape(function_name)}$",   # match only exact old_name
                cache={}
            )

        if os.path.exists(code):
            with open(code, "w", encoding="utf-8") as file:
                file.write(updated_code)
            return str(code)
        else:
            return updated_code
    

    def replace_in_function(self, code: str, function_name: str, replacements: List[Dict[str, str]]) -> str:
        """
        Replace specific strings within a function definition.
        
        Args:
            code: The source code or file path
            function_name: The name of the function to modify
            replacements: List of dicts with 'old' and 'new' keys for string replacements
            
        Returns:
            The updated code or file path if code was a file path
        """
        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code_bytes = bytearray(file.read(), 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')

        tree = self.parser.parse(code_bytes)

        # Locate definition of function_name
        function_node = None
        root = tree.root_node
        stack = [root]
        while stack:
            node = stack.pop()
            
            if node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                id_stack = [declarator]
                while id_stack:
                    child_decorator = id_stack.pop()
                    if child_decorator.type == "identifier":
                        identifier_name = code_bytes[child_decorator.start_byte:child_decorator.end_byte].decode('utf8')
                
                        if identifier_name == function_name:
                            print(f"Found {function_name} in {code[:min(len(code), 100)]}...")
                            function_node = node
                            break
                        break

                    id_stack.extend(reversed(child_decorator.children))
            stack.extend(node.children)
        
        if function_node is None:
            raise ValueError(f"No function definition for '{function_name}' found in {code}")

        # Extract function text and apply replacements
        function_text = code_bytes[function_node.start_byte:function_node.end_byte].decode('utf8')
        
        for replacement in replacements:
            old_str = replacement.get('old', '')
            new_str = replacement.get('new', '')
            if old_str:
                function_text = function_text.replace(old_str, new_str)
        
        # Replace the function in the original code
        code_bytes[function_node.start_byte:function_node.end_byte] = function_text.encode("utf-8")
        updated_code = code_bytes.decode("utf-8")

        if os.path.exists(code):
            with open(code, "w", encoding="utf-8") as file:
                file.write(updated_code)
            return str(code)
        else:
            return updated_code

    def remove_comments_from_source(self, code: str) -> str:
        """
        Remove all comments from the given source code using tree-sitter.

        Args:
            source_code: The source code to remove comments from

        Returns:
            The source code with all comments removed
        """
        

        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code_bytes = bytearray(file.read(), 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')

        tree = self.parser.parse(code_bytes)

        try:
            # Find all comment nodes
            results = self.c_language.query('(comment) @comment').captures(tree.root_node)
            if not "comment" in results:
                # Silently handle case where no comments are found
                return code
            
            comment_nodes = []
            for node in results['comment']:
                comment_nodes.append(node)

            # If no comments found, return the original source code
            if not comment_nodes:
                return code

            # Sort comment nodes by start position in reverse order
            # (to avoid invalidating positions when removing comments)
            comment_nodes.sort(key=lambda node: node.start_byte, reverse=True)

            # Remove each comment
            for node in comment_nodes:
                code_bytes = code_bytes[:node.start_byte] + code_bytes[node.end_byte:]

            # Convert back to string
            return code_bytes.decode('utf8')
        
        except Exception as e:
            print(f"Error removing comments:\n{e}")
            return code

    def process_row(self, row, function_column='function', new_function_column='generated_function', code_column='file', output_column='updated_file'):
        """
        Process a row from a DataFrame, replacing a function in the file with a new function.
        
        Args:
            row: The DataFrame row
            function_column: Column containing the function name to replace
            new_function_column: Column containing the new function definition
            file_column: Column containing the file path
            output_column: Column to store the output file path
            
        Returns:
            The updated row
        """
        row[output_column] = None
        code = row[code_column]
        function_name = row[function_column]
        new_function = row[new_function_column]
        
        if pd.isna(code) or pd.isna(function_name) or pd.isna(new_function):
            return row
        
        if not code or not code.strip():
            print(f"Empty code in row: {row}")
            return row
        
        # if not os.path.exists(file_path):
        #     print(f"File not found: {file_path}")
        #     return row
        
        try:
            output_path = self.replace_function_definition(code, function_name, new_function)
            row[output_column] = output_path
            return row
        except Exception as e:
            print(f"Error processing file {code}: {e}")
            return row
        
    def print_tree_structure_recursive(self, node: Node, code_bytes: bytes, depth: int = 0, print_id = False):
        # Print the current node with proper indentation
        node_text = code_bytes[node.start_byte:node.end_byte].decode('utf8')
        # Truncate long text for readability
        if len(node_text) > 50:
            node_text = node_text[:70] + "..."
        
        # Replace newlines with spaces for compact display
        node_text = node_text.replace('\n', ' ')
        id_text = f"id: {node.id}, " if print_id else ""
        print(f"{'  ' * depth}{id_text}{node.type}: {node_text}")
        
        # Recursively process all children with increased depth
        for child in node.children:
            self.print_tree_structure_recursive(child, code_bytes, depth + 1)

    def print_tree_structure(self, code: str, print_id = False):
        """
        Print the tree structure of the given code.

        Args:
            code: The source code as a string
        """
        if os.path.exists(code):
            with open(code, "r", encoding="utf-8") as file:
                code = file.read()
                code_bytes = bytearray(code, 'utf8')
        else:
            code_bytes = bytearray(code, 'utf8')
            
        tree = self.parser.parse(code_bytes)
        self.print_tree_structure_recursive(tree.root_node, code_bytes, print_id = False)

if __name__ == "__main__":
    print("Loading data...")
    data = pd.read_csv('QA4BinVul_with_functions.csv')
    replacer = FunctionReplacer()
    
    # Use apply with progress bar
    processed_data = data.progress_apply(
        lambda row: replacer.process_row(row), 
        axis=1
    )

    # Display some statistics
    updated_files_count = (processed_data['updated_file'].str.strip() != '').sum()

    print(f"\nStatistics:")
    print(f"Total rows processed: {len(processed_data)}")
    print(f"Files updated: {updated_files_count} ({updated_files_count/len(processed_data)*100:.2f}%)")

    processed_data.to_csv("QA4BinVul_updated.csv", index=False)